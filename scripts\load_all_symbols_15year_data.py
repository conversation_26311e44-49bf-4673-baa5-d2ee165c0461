#!/usr/bin/env python3
"""
15-Year Historical Data Loading Script for All NSE Symbols
Loads 15 years of 1-minute data for all symbols from NSE_CM.csv for nightly runs.
"""

import sys
import os
import argparse
import signal
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Optional

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.logging import get_logger
from app.database.connection import get_db, check_database_connection
from app.services.chunked_data_loader import ChunkedDataLoader, LoadingSession
from app.services.progress_manager import ProgressManager
from app.services.data_service import DataService
from app.services.symbol_mapping_service import SymbolMappingService

logger = get_logger(__name__)

class AllSymbolsDataManager:
    """Manager for loading 15-year historical data for all NSE symbols."""
    
    def __init__(self):
        """Initialize the manager."""
        self.db = None
        self.chunked_loader = None
        self.progress_manager = None
        self.data_service = None
        self.symbol_service = None
        self.interrupted = False
        self.current_operation_id = None
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle interrupt signals gracefully."""
        logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.interrupted = True
        
        if self.progress_manager and self.current_operation_id:
            self.progress_manager.pause_operation(self.current_operation_id)
            logger.info("Operation paused - can be resumed later")
    
    def initialize_services(self) -> bool:
        """Initialize all required services."""
        try:
            # Check database connection
            if not check_database_connection():
                logger.error("Database connection failed")
                return False
            
            # Initialize database session
            self.db = next(get_db())
            
            # Initialize services
            self.chunked_loader = ChunkedDataLoader(
                db=self.db,
                chunk_size_days=90,  # 90-day chunks (within 100-day API limit)
                max_retries_per_chunk=3
            )
            
            self.progress_manager = ProgressManager()
            self.data_service = DataService(self.db)
            self.symbol_service = SymbolMappingService()
            
            logger.info("✓ Services initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize services: {e}")
            return False
    
    def get_symbols_to_load(self, symbol_filter: str = None) -> List[str]:
        """Get list of symbols to load data for."""
        try:
            logger.info("📋 Getting symbols to load...")
            
            if symbol_filter == "nifty50":
                # Load NIFTY 50 constituents + major indices
                symbols = ["NIFTY", "BANKNIFTY", "FINNIFTY"]
                nifty50_mappings = self.symbol_service.get_nifty50_mappings()
                symbols.extend(list(nifty50_mappings.keys()))
                logger.info(f"Selected NIFTY 50 symbols: {len(symbols)} symbols")
                
            elif symbol_filter == "indices":
                # Load only indices
                index_mappings = self.symbol_service.get_index_mappings()
                symbols = list(index_mappings.keys())
                logger.info(f"Selected indices: {len(symbols)} symbols")
                
            elif symbol_filter == "all":
                # Load all available symbols from NSE_CM.csv
                logger.info("Loading all symbols from NSE_CM.csv...")
                all_mappings = {}
                
                # Download and parse NSE_CM.csv
                for url in self.symbol_service.csv_urls:
                    if "NSE_CM.csv" in url:
                        csv_path = self.symbol_service.download_csv_file(url)
                        if csv_path:
                            mappings = self.symbol_service.parse_csv_file(csv_path)
                            for mapping in mappings:
                                # Filter for equity stocks and indices only
                                if (mapping.instrument_type in ['0', '10'] and  # 0=equity, 10=index
                                    not mapping.is_index or mapping.db_symbol in ['NIFTY', 'BANKNIFTY', 'FINNIFTY']):
                                    all_mappings[mapping.db_symbol] = mapping
                
                symbols = list(all_mappings.keys())
                logger.info(f"Selected all symbols: {len(symbols)} symbols")
                
            else:
                # Default to NIFTY only
                symbols = ["NIFTY"]
                logger.info("Selected NIFTY only")
            
            return symbols
            
        except Exception as e:
            logger.error(f"Error getting symbols to load: {e}")
            return []
    
    def calculate_date_range(self, years: int = 15) -> tuple:
        """Calculate start and end dates for data loading."""
        end_date = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        start_date = end_date - timedelta(days=years * 365)
        
        logger.info(f"Date range: {start_date.date()} to {end_date.date()} ({years} years)")
        return start_date, end_date
    
    def load_symbol_data(self, symbol: str, start_date: datetime, end_date: datetime) -> bool:
        """Load 15-year data for a single symbol."""
        try:
            logger.info(f"🔄 Loading data for {symbol}...")
            
            # Check existing data
            stats = self.data_service.get_data_statistics(symbol)
            if stats and stats.get('total_records', 0) > 0:
                logger.info(f"   Existing records: {stats['total_records']:,}")
            
            # Create loading session
            session = self.chunked_loader.create_loading_session(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                chunk_size_days=90
            )
            
            if not session:
                logger.error(f"   Failed to create session for {symbol}")
                return False
            
            logger.info(f"   Created session: {session.session_id} ({len(session.chunks)} chunks)")
            
            # Start progress tracking for this symbol
            operation_id = f"load_15year_{symbol}_{session.session_id}"
            self.current_operation_id = operation_id
            
            self.progress_manager.start_operation(
                operation_id=operation_id,
                operation_type="15_year_symbol_data_load",
                total_steps=len(session.chunks),
                metadata={
                    'symbol': symbol,
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'session_id': session.session_id
                }
            )
            
            # Load data
            success = self.chunked_loader.load_session_data(session)
            
            # Complete progress tracking
            if success:
                self.progress_manager.complete_operation(operation_id, success=True)
                logger.info(f"   ✅ {symbol}: Data loading completed successfully")
            else:
                self.progress_manager.complete_operation(operation_id, success=False, 
                                                       error_message="Data loading failed")
                logger.error(f"   ❌ {symbol}: Data loading failed")
            
            return success
            
        except Exception as e:
            logger.error(f"Error loading data for {symbol}: {e}")
            if self.current_operation_id:
                self.progress_manager.complete_operation(self.current_operation_id, success=False, 
                                                       error_message=str(e))
            return False
    
    def run_bulk_load(self, symbols: List[str], years: int = 15) -> Dict[str, bool]:
        """Run bulk data loading for multiple symbols."""
        try:
            start_date, end_date = self.calculate_date_range(years)
            
            logger.info(f"🚀 Starting bulk data loading for {len(symbols)} symbols...")
            logger.info("=" * 80)
            
            results = {}
            successful_loads = 0
            failed_loads = 0
            
            for i, symbol in enumerate(symbols, 1):
                if self.interrupted:
                    logger.info("⚠️  Operation interrupted by user")
                    break
                
                logger.info(f"\n📊 Processing symbol {i}/{len(symbols)}: {symbol}")
                
                success = self.load_symbol_data(symbol, start_date, end_date)
                results[symbol] = success
                
                if success:
                    successful_loads += 1
                else:
                    failed_loads += 1
                
                # Progress summary
                logger.info(f"   Progress: {i}/{len(symbols)} symbols processed")
                logger.info(f"   Success: {successful_loads}, Failed: {failed_loads}")
                
                # Rate limiting between symbols
                if i < len(symbols):
                    time.sleep(2)  # 2-second delay between symbols
            
            # Final summary
            logger.info("\n" + "=" * 80)
            logger.info("📈 BULK LOADING SUMMARY")
            logger.info("=" * 80)
            logger.info(f"Total symbols processed: {len(results)}")
            logger.info(f"Successful loads: {successful_loads}")
            logger.info(f"Failed loads: {failed_loads}")
            logger.info(f"Success rate: {(successful_loads/len(results)*100):.1f}%")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in bulk loading: {e}")
            return {}
    
    def cleanup(self):
        """Cleanup resources."""
        if self.db:
            self.db.close()
            logger.info("✓ Database connection closed")

def main():
    """Main execution function."""
    parser = argparse.ArgumentParser(description="Load 15 years of historical data for all NSE symbols")
    parser.add_argument("--years", type=int, default=15, 
                       help="Number of years to load (default: 15)")
    parser.add_argument("--symbols", choices=["nifty50", "indices", "all", "nifty"], default="nifty50",
                       help="Symbol set to load (default: nifty50)")
    parser.add_argument("--dry-run", action="store_true",
                       help="Show symbols that would be loaded without actually loading")
    
    args = parser.parse_args()
    
    logger.info("🚀 15-Year Historical Data Loading Script for All NSE Symbols")
    logger.info("=" * 80)
    
    manager = AllSymbolsDataManager()
    
    try:
        # Initialize services
        logger.info("Step 1: Initializing services...")
        if not manager.initialize_services():
            return False
        
        # Get symbols to load
        logger.info("\nStep 2: Getting symbols to load...")
        symbols = manager.get_symbols_to_load(args.symbols)
        
        if not symbols:
            logger.error("No symbols found to load")
            return False
        
        if args.dry_run:
            logger.info(f"\n📋 DRY RUN - Would load data for {len(symbols)} symbols:")
            for i, symbol in enumerate(symbols, 1):
                logger.info(f"  {i:3d}. {symbol}")
            return True
        
        # Starting automated data loading for nightly runs
        logger.info(f"\n🚀 Starting automated load of {args.years} years of data for {len(symbols)} symbols")
        logger.info("This operation may take several hours to complete.")
        
        # Start bulk loading
        logger.info("\nStep 3: Starting bulk data loading...")
        results = manager.run_bulk_load(symbols, args.years)
        
        # Show final results
        successful_symbols = [s for s, success in results.items() if success]
        failed_symbols = [s for s, success in results.items() if not success]
        
        if successful_symbols:
            logger.info(f"\n✅ Successfully loaded data for {len(successful_symbols)} symbols")
        
        if failed_symbols:
            logger.info(f"\n❌ Failed to load data for {len(failed_symbols)} symbols:")
            for symbol in failed_symbols:
                logger.info(f"   - {symbol}")
        
        return len(failed_symbols) == 0
        
    except KeyboardInterrupt:
        logger.info("\n⚠️  Operation interrupted by user")
        return False
    except Exception as e:
        logger.error(f"\n❌ Unexpected error: {e}")
        return False
    finally:
        manager.cleanup()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
