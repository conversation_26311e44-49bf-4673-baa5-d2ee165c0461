"""
Authentication API endpoints.
"""

from datetime import <PERSON><PERSON><PERSON>
from typing import Dict, Any
from fastapi import <PERSON><PERSON>outer, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from pydantic import BaseModel

from app.api.auth import auth_manager, get_current_user, create_demo_token
from app.core.logging import get_logger

logger = get_logger(__name__)
router = APIRouter()


class Token(BaseModel):
    """Token response model."""
    access_token: str
    token_type: str
    expires_in: int


class TokenData(BaseModel):
    """Token data model."""
    user_id: str
    permissions: list


class UserInfo(BaseModel):
    """User information model."""
    user_id: str
    permissions: list
    active: bool


class LoginRequest(BaseModel):
    """Login request model."""
    username: str
    password: str


class APIKeyRequest(BaseModel):
    """API key request model."""
    name: str
    permissions: list


class APIKeyResponse(BaseModel):
    """API key response model."""
    api_key: str
    name: str
    permissions: list
    created_at: str


@router.post("/login", response_model=Token)
async def login(form_data: OAuth2PasswordRequestForm = Depends()):
    """Login with username and password."""
    try:
        # For demo purposes, accept any username/password
        # In production, validate against database
        if not form_data.username or not form_data.password:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username and password required"
            )
        
        # Demo user validation
        if form_data.username == "demo" and form_data.password == "demo123":
            user_data = {
                "sub": form_data.username,
                "user_id": form_data.username,
                "permissions": ["read", "write"],
                "active": True
            }
        elif form_data.username == "readonly" and form_data.password == "readonly123":
            user_data = {
                "sub": form_data.username,
                "user_id": form_data.username,
                "permissions": ["read"],
                "active": True
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid username or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        access_token_expires = timedelta(minutes=30)
        access_token = auth_manager.create_access_token(
            data=user_data,
            expires_delta=access_token_expires
        )
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=1800  # 30 minutes
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/demo-token", response_model=Token)
async def get_demo_token():
    """Get a demo token for testing."""
    try:
        access_token = create_demo_token()
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=1800  # 30 minutes
        )
        
    except Exception as e:
        logger.error(f"Demo token error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create demo token"
        )


@router.get("/me", response_model=UserInfo)
async def get_current_user_info(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Get current user information."""
    try:
        return UserInfo(
            user_id=current_user.get("user_id", "unknown"),
            permissions=current_user.get("permissions", []),
            active=current_user.get("active", True)
        )
        
    except Exception as e:
        logger.error(f"Get user info error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user information"
        )


@router.post("/refresh", response_model=Token)
async def refresh_token(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Refresh access token."""
    try:
        # Create new token with same user data
        access_token_expires = timedelta(minutes=30)
        access_token = auth_manager.create_access_token(
            data=current_user,
            expires_delta=access_token_expires
        )
        
        return Token(
            access_token=access_token,
            token_type="bearer",
            expires_in=1800  # 30 minutes
        )
        
    except Exception as e:
        logger.error(f"Token refresh error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to refresh token"
        )


@router.post("/logout")
async def logout(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Logout user (invalidate token)."""
    try:
        # In a real implementation, you would add the token to a blacklist
        # For now, just return success
        return {"message": "Successfully logged out"}
        
    except Exception as e:
        logger.error(f"Logout error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.get("/validate")
async def validate_token(current_user: Dict[str, Any] = Depends(get_current_user)):
    """Validate current token."""
    try:
        return {
            "valid": True,
            "user_id": current_user.get("user_id"),
            "permissions": current_user.get("permissions", [])
        }
        
    except Exception as e:
        logger.error(f"Token validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token validation failed"
        )


@router.get("/permissions")
async def get_available_permissions():
    """Get list of available permissions."""
    return {
        "permissions": [
            {
                "name": "read",
                "description": "Read access to data and endpoints"
            },
            {
                "name": "write",
                "description": "Write access to create and modify data"
            },
            {
                "name": "admin",
                "description": "Administrative access to all features"
            }
        ]
    }


@router.get("/demo-credentials")
async def get_demo_credentials():
    """Get demo credentials for testing."""
    return {
        "credentials": [
            {
                "username": "demo",
                "password": "demo123",
                "permissions": ["read", "write"],
                "description": "Full access demo user"
            },
            {
                "username": "readonly",
                "password": "readonly123",
                "permissions": ["read"],
                "description": "Read-only demo user"
            }
        ],
        "api_keys": [
            {
                "key": "dev-key-123",
                "permissions": ["read", "write"],
                "description": "Development API key"
            },
            {
                "key": "readonly-key-456",
                "permissions": ["read"],
                "description": "Read-only API key"
            }
        ]
    }
