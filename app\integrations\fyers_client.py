"""
Fyers API client for market data integration.
"""

from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import requests
import json
from dataclasses import dataclass

from app.core.logging import get_logger

logger = get_logger(__name__)


@dataclass
class OHLCVData:
    """OHLCV data structure."""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int


class FyersClient:
    """Fyers API client for market data."""
    
    def __init__(self):
        """Initialize Fyers client."""
        self.client_id = None
        self.access_token = None
        self.base_url = "https://api.fyers.in/api/v2"
        self.authenticated = False
        
        # Load credentials if available
        self._load_credentials()
    
    def _load_credentials(self):
        """Load Fyers credentials from files."""
        try:
            # Load client ID
            try:
                with open('fyers_client_id.txt', 'r') as f:
                    self.client_id = f.read().strip()
            except FileNotFoundError:
                logger.warning("fyers_client_id.txt not found")
            
            # Load access token
            try:
                with open('fyers_access_token.txt', 'r') as f:
                    self.access_token = f.read().strip()
                    if self.access_token:
                        self.authenticated = True
            except FileNotFoundError:
                logger.warning("fyers_access_token.txt not found")
                
        except Exception as e:
            logger.error(f"Error loading Fyers credentials: {e}")
    
    def is_authenticated(self) -> bool:
        """Check if client is authenticated."""
        return self.authenticated and self.access_token is not None
    
    def get_historical_data(
        self,
        symbol: str,
        timeframe: str,
        start_date: datetime,
        end_date: datetime
    ) -> List[OHLCVData]:
        """
        Get historical OHLCV data from Fyers.
        
        Args:
            symbol: Symbol to fetch data for
            timeframe: Timeframe (1, 5, 15, 30, 60, 1D)
            start_date: Start date
            end_date: End date
            
        Returns:
            List of OHLCV data
        """
        try:
            if not self.is_authenticated():
                logger.error("Fyers client not authenticated")
                return []
            
            # For demo purposes, return mock data
            logger.warning("Using mock data - Fyers API not fully integrated")
            return self._generate_mock_data(symbol, start_date, end_date)
            
        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            return []
    
    def _generate_mock_data(
        self,
        symbol: str,
        start_date: datetime,
        end_date: datetime
    ) -> List[OHLCVData]:
        """Generate mock OHLCV data for testing."""
        import random
        
        data = []
        current_date = start_date
        base_price = 20000.0  # Base price for NIFTY
        
        while current_date <= end_date:
            # Skip weekends
            if current_date.weekday() >= 5:
                current_date += timedelta(days=1)
                continue
            
            # Generate trading hours data (9:15 AM to 3:30 PM)
            trading_start = current_date.replace(hour=9, minute=15, second=0, microsecond=0)
            trading_end = current_date.replace(hour=15, minute=30, second=0, microsecond=0)
            
            current_time = trading_start
            daily_open = base_price * (1 + random.uniform(-0.02, 0.02))
            current_price = daily_open
            
            while current_time <= trading_end:
                # Generate realistic price movement
                price_change = random.uniform(-0.005, 0.005)
                new_price = current_price * (1 + price_change)
                
                # Generate OHLC for this minute
                high = max(current_price, new_price) * (1 + abs(random.uniform(0, 0.002)))
                low = min(current_price, new_price) * (1 - abs(random.uniform(0, 0.002)))
                volume = random.randint(50000, 200000)
                
                data.append(OHLCVData(
                    timestamp=current_time,
                    open=current_price,
                    high=high,
                    low=low,
                    close=new_price,
                    volume=volume
                ))
                
                current_price = new_price
                current_time += timedelta(minutes=1)
            
            # Update base price for next day
            base_price = current_price
            current_date += timedelta(days=1)
        
        return data
    
    def get_quotes(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        Get real-time quotes for symbols.
        
        Args:
            symbols: List of symbols
            
        Returns:
            Dictionary of symbol quotes
        """
        try:
            if not self.is_authenticated():
                logger.error("Fyers client not authenticated")
                return {}
            
            # For demo purposes, return mock quotes
            logger.warning("Using mock quotes - Fyers API not fully integrated")
            return self._generate_mock_quotes(symbols)
            
        except Exception as e:
            logger.error(f"Error fetching quotes: {e}")
            return {}
    
    def _generate_mock_quotes(self, symbols: List[str]) -> Dict[str, Dict[str, Any]]:
        """Generate mock quotes for testing."""
        import random
        
        quotes = {}
        base_prices = {
            'NIFTY': 20000,
            'BANKNIFTY': 45000,
            'SENSEX': 65000,
            'RELIANCE': 2500,
            'TCS': 3500,
            'INFY': 1500
        }
        
        for symbol in symbols:
            base_price = base_prices.get(symbol, 1000)
            current_price = base_price * (1 + random.uniform(-0.02, 0.02))
            change = current_price - base_price
            change_percent = (change / base_price) * 100
            
            quotes[symbol] = {
                'symbol': symbol,
                'ltp': current_price,
                'open': base_price * (1 + random.uniform(-0.01, 0.01)),
                'high': current_price * (1 + abs(random.uniform(0, 0.015))),
                'low': current_price * (1 - abs(random.uniform(0, 0.015))),
                'close': base_price,
                'chng': change,
                'chp': change_percent,
                'volume': random.randint(1000000, ********),
                'timestamp': datetime.now().isoformat()
            }
        
        return quotes
    
    def get_market_status(self) -> Dict[str, Any]:
        """Get market status."""
        try:
            # For demo purposes, return mock status
            current_time = datetime.now()
            is_market_hours = (
                current_time.weekday() < 5 and  # Monday to Friday
                9 <= current_time.hour < 16  # 9 AM to 4 PM
            )
            
            return {
                'market_status': 'OPEN' if is_market_hours else 'CLOSED',
                'timestamp': current_time.isoformat(),
                'next_session': 'Regular trading session',
                'exchange': 'NSE'
            }
            
        except Exception as e:
            logger.error(f"Error getting market status: {e}")
            return {
                'market_status': 'UNKNOWN',
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def authenticate(self, client_id: str, access_token: str) -> bool:
        """
        Authenticate with Fyers API.
        
        Args:
            client_id: Fyers client ID
            access_token: Fyers access token
            
        Returns:
            True if authentication successful
        """
        try:
            self.client_id = client_id
            self.access_token = access_token
            
            # Save credentials
            with open('fyers_client_id.txt', 'w') as f:
                f.write(client_id)
            
            with open('fyers_access_token.txt', 'w') as f:
                f.write(access_token)
            
            self.authenticated = True
            logger.info("Fyers authentication successful")
            return True
            
        except Exception as e:
            logger.error(f"Fyers authentication failed: {e}")
            return False
